/**
 * Админ панель SMS Proxy Service
 * Главный файл приложения - управляет всеми модулями
 */

/**
 * Главный класс админ панели
 * Координирует работу всех модулей
 */
class AdminPanel {
    constructor() {
        this.currentSection = 'dashboard';
        this.modules = {};
        this.paginationManagers = {};
        this.isInitialized = false;
    }

    /**
     * Инициализация админ панели
     */
    init() {
        if (this.isInitialized) return;

        console.log('Инициализация админ панели');

        // Инициализируем основные компоненты
        this.initializeCore();

        // Инициализируем модули
        this.initializeModules();

        // Настраиваем навигацию
        this.setupNavigation();

        // Инициализируем менеджеры пагинации
        this.initializePaginationManagers();

        this.isInitialized = true;
        console.log('Админ панель инициализирована');
    }

    /**
     * Инициализация основных компонентов
     */
    initializeCore() {
        // Инициализируем UI менеджер
        if (window.uiManager) {
            uiManager.init();
        }

        // Инициализируем менеджер авторизации
        if (window.authManager) {
            authManager.init();

            // Подписываемся на события авторизации
            authManager.on('onLogin', () => {
                this.onUserLogin();
            });

            authManager.on('onLogout', () => {
                this.onUserLogout();
            });
        }
    }

    /**
     * Инициализация модулей
     */
    initializeModules() {
        // Инициализируем модуль дашборда
        if (window.dashboardModule) {
            this.modules.dashboard = dashboardModule;
            dashboardModule.init();
        }

        // Инициализируем модуль пользователей
        if (window.usersModule) {
            this.modules.users = usersModule;
            usersModule.init();
        }

        console.log('Модули инициализированы:', Object.keys(this.modules));
    }

    /**
     * Настройка навигации
     */
    setupNavigation() {
        // Навигация по секциям
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.switchSection(section);

                // Закрываем мобильное меню после выбора секции
                if (uiManager) {
                    uiManager.closeMobileSidebar();
                }
            });
        });
    }

    /**
     * Инициализация менеджеров пагинации
     */
    initializePaginationManagers() {
        // Менеджер пагинации для активаций
        this.paginationManagers.activations = new PaginationManager(
            'activations-content',
            async (params) => {
                return await apiClient.get(AppConstants.API_ENDPOINTS.ACTIVATIONS, params);
            },
            {
                pageSize: AppConstants.PAGINATION_DEFAULTS.PAGE_SIZE,
                pageSizeOptions: AppConstants.PAGINATION_DEFAULTS.PAGE_SIZE_OPTIONS
            }
        );

        // Переопределяем метод renderContent для активаций
        this.paginationManagers.activations.renderContent = (activations) => {
            this.renderActivationsTable(activations);
        };

        // Менеджер пагинации для транзакций
        this.paginationManagers.transactions = new PaginationManager(
            'transactions-content',
            async (params) => {
                return await apiClient.get(AppConstants.API_ENDPOINTS.TRANSACTIONS, params);
            },
            {
                pageSize: AppConstants.PAGINATION_DEFAULTS.PAGE_SIZE,
                pageSizeOptions: AppConstants.PAGINATION_DEFAULTS.PAGE_SIZE_OPTIONS
            }
        );

        // Переопределяем метод renderContent для транзакций
        this.paginationManagers.transactions.renderContent = (transactions) => {
            this.renderTransactionsTable(transactions);
        };

        // Менеджер пагинации для сервисов
        this.paginationManagers.services = new PaginationManager(
            'services-content',
            async (params) => {
                return await apiClient.get(AppConstants.API_ENDPOINTS.SERVICES, params);
            },
            {
                pageSize: AppConstants.PAGINATION_DEFAULTS.PAGE_SIZE,
                pageSizeOptions: AppConstants.PAGINATION_DEFAULTS.PAGE_SIZE_OPTIONS
            }
        );

        // Переопределяем метод renderContent для сервисов
        this.paginationManagers.services.renderContent = (services) => {
            this.renderServicesTable(services);
        };

        // Менеджер пагинации для логов
        this.paginationManagers.logs = new PaginationManager(
            'logs-content',
            async (params) => {
                return await apiClient.get(AppConstants.API_ENDPOINTS.LOGS, params);
            },
            {
                pageSize: AppConstants.PAGINATION_DEFAULTS.PAGE_SIZE,
                pageSizeOptions: AppConstants.PAGINATION_DEFAULTS.PAGE_SIZE_OPTIONS
            }
        );

        // Переопределяем метод renderContent для логов
        this.paginationManagers.logs.renderContent = (logs) => {
            this.renderLogsTable(logs);
        };
    }

    /**
     * Переключение между секциями
     * @param {string} section - Название секции
     */
    switchSection(section) {
        // Скрываем все секции
        document.querySelectorAll('.content-section').forEach(el => {
            el.style.display = 'none';
        });

        // Убираем активный класс с навигации
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Показываем нужную секцию
        const sectionElement = document.getElementById(`${section}-section`);
        if (sectionElement) {
            sectionElement.style.display = 'block';
        }

        // Добавляем активный класс
        const activeLink = document.querySelector(`[data-section="${section}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        this.currentSection = section;

        // Загружаем данные для секции
        this.loadSectionData(section);
    }

    /**
     * Загрузка данных для секции
     * @param {string} section - Название секции
     */
    async loadSectionData(section) {
        try {
            switch (section) {
                case 'dashboard':
                    if (this.modules.dashboard) {
                        await this.modules.dashboard.loadDashboard();
                    }
                    break;
                case 'users':
                    if (this.modules.users) {
                        await this.modules.users.loadUsers();
                    }
                    break;
                case 'activations':
                    if (this.paginationManagers.activations) {
                        await this.paginationManagers.activations.loadData();
                    }
                    break;
                case 'transactions':
                    if (this.paginationManagers.transactions) {
                        await this.paginationManagers.transactions.loadData();
                    }
                    break;
                case 'services':
                    if (this.paginationManagers.services) {
                        await this.paginationManagers.services.loadData();
                    }
                    break;
                case 'countries':
                    await this.loadCountries();
                    break;
                case 'prices':
                    await this.loadPrices();
                    break;
                case 'settings':
                    await this.loadSettings();
                    break;
                case 'logs':
                    if (this.paginationManagers.logs) {
                        await this.paginationManagers.logs.loadData();
                    }
                    break;
                case 'providers':
                    await this.loadProviders();
                    break;
            }
        } catch (error) {
            console.error(`Ошибка загрузки данных для секции ${section}:`, error);
            if (uiManager) {
                uiManager.showError(`Ошибка загрузки данных: ${error.message}`);
            }
        }
    }

    /**
     * Обработчики событий авторизации
     */
    onUserLogin() {
        console.log('Пользователь вошел в систему');
        // Загружаем дашборд при входе
        if (this.currentSection === 'dashboard' && this.modules.dashboard) {
            this.modules.dashboard.loadDashboard();
        }
    }

    onUserLogout() {
        console.log('Пользователь вышел из системы');
        // Очищаем данные при выходе
        this.currentSection = 'dashboard';
    }

    /**
     * Отображение таблицы активаций
     * @param {Array} activations - Массив активаций
     */
    renderActivationsTable(activations) {
        const container = document.getElementById('activations-content');
        if (!container) return;

        const html = `
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-phone-vibrate"></i> Список активаций</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Пользователь</th>
                                    <th>Сервис</th>
                                    <th>Страна</th>
                                    <th>Номер телефона</th>
                                    <th>Статус</th>
                                    <th>SMS код</th>
                                    <th>Дата создания</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${activations.map(activation => `
                                    <tr>
                                        <td>${activation.id}</td>
                                        <td>${activation.user ? DataFormatters.escapeHtml(activation.user.username) : activation.user_id}</td>
                                        <td>${activation.service ? DataFormatters.escapeHtml(activation.service.name) : activation.service_id}</td>
                                        <td>${activation.country ? DataFormatters.escapeHtml(activation.country.name) : activation.country_id}</td>
                                        <td>${DataFormatters.formatPhone(activation.phone_number)}</td>
                                        <td><span class="badge bg-${AppConstants.getStatusColor(activation.status)}">${activation.status}</span></td>
                                        <td>${activation.sms_code || '-'}</td>
                                        <td>${DataFormatters.formatDate(activation.ordered_at)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        // Находим контейнер для контента (исключая пагинацию)
        let contentContainer = container.querySelector('.content-wrapper');
        if (!contentContainer) {
            contentContainer = document.createElement('div');
            contentContainer.className = 'content-wrapper';
            container.appendChild(contentContainer);
        }
        contentContainer.innerHTML = html;
    }

    /**
     * Отображение таблицы транзакций
     * @param {Array} transactions - Массив транзакций
     */
    renderTransactionsTable(transactions) {
        const container = document.getElementById('transactions-content');
        if (!container) return;

        const html = `
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-credit-card"></i> История транзакций</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Пользователь</th>
                                    <th>Тип</th>
                                    <th>Сумма</th>
                                    <th>Описание</th>
                                    <th>Дата</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${transactions.map(tx => `
                                    <tr>
                                        <td>${tx.id}</td>
                                        <td>${tx.user ? DataFormatters.escapeHtml(tx.user.username) : tx.user_id}</td>
                                        <td><span class="badge bg-${AppConstants.getTransactionColor(tx.type)}">${tx.type}</span></td>
                                        <td>${DataFormatters.formatMoney(tx.amount)}</td>
                                        <td>${DataFormatters.escapeHtml(tx.comment) || '-'}</td>
                                        <td>${DataFormatters.formatDate(tx.timestamp)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        let contentContainer = container.querySelector('.content-wrapper');
        if (!contentContainer) {
            contentContainer = document.createElement('div');
            contentContainer.className = 'content-wrapper';
            container.appendChild(contentContainer);
        }
        contentContainer.innerHTML = html;
    }

    /**
     * Отображение таблицы сервисов
     * @param {Array} services - Массив сервисов
     */
    renderServicesTable(services) {
        const container = document.getElementById('services-content');
        if (!container) return;

        const html = `
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-gear"></i> Список сервисов</h5>
                    <button class="btn btn-primary" onclick="adminPanel.showServiceModal()">
                        <i class="bi bi-plus"></i> Добавить сервис
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Код</th>
                                    <th>Название</th>
                                    <th>Активен</th>
                                    <th>Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${services.map(service => `
                                    <tr>
                                        <td>${service.id}</td>
                                        <td><code>${DataFormatters.escapeHtml(service.code)}</code></td>
                                        <td>${DataFormatters.escapeHtml(service.name)}</td>
                                        <td><span class="badge bg-${service.is_active ? 'success' : 'secondary'}">${service.is_active ? 'Да' : 'Нет'}</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1" onclick="adminPanel.editService(${service.id})" title="Редактировать">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="adminPanel.deleteService(${service.id})" title="Удалить">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        let contentContainer = container.querySelector('.content-wrapper');
        if (!contentContainer) {
            contentContainer = document.createElement('div');
            contentContainer.className = 'content-wrapper';
            container.appendChild(contentContainer);
        }
        contentContainer.innerHTML = html;
    }

    /**
     * Отображение таблицы логов
     * @param {Array} logs - Массив логов
     */
    renderLogsTable(logs) {
        const container = document.getElementById('logs-content');
        if (!container) return;

        const html = `
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-journal-text"></i> Логи системы</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Пользователь</th>
                                    <th>Действие</th>
                                    <th>Детали</th>
                                    <th>IP адрес</th>
                                    <th>Дата</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${logs.map(log => `
                                    <tr>
                                        <td>${log.id}</td>
                                        <td>${log.user ? DataFormatters.escapeHtml(log.user.username) : (log.user_id || '-')}</td>
                                        <td><span class="badge bg-info">${DataFormatters.escapeHtml(log.action)}</span></td>
                                        <td>${DataFormatters.escapeHtml(log.details) || '-'}</td>
                                        <td>${DataFormatters.formatIpAddress(log.ip_address)}</td>
                                        <td>${DataFormatters.formatDate(log.timestamp)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        let contentContainer = container.querySelector('.content-wrapper');
        if (!contentContainer) {
            contentContainer = document.createElement('div');
            contentContainer.className = 'content-wrapper';
            container.appendChild(contentContainer);
        }
        contentContainer.innerHTML = html;
    }

    /**
     * Заглушки для методов, которые будут реализованы в отдельных модулях
     */
    async loadCountries() {
        console.log('loadCountries - будет реализовано в модуле стран');
    }

    async loadPrices() {
        console.log('loadPrices - будет реализовано в модуле цен');
    }

    async loadSettings() {
        console.log('loadSettings - будет реализовано в модуле настроек');
    }

    async loadProviders() {
        console.log('loadProviders - будет реализовано в модуле провайдеров');
    }
}

// Создаем глобальный экземпляр админ панели
window.adminPanel = new AdminPanel();

// Инициализируем приложение после загрузки DOM
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM загружен, инициализируем админ панель');
    if (window.adminPanel) {
        adminPanel.init();
    }
});

    /**
     * Загрузка стран
     */
    async loadCountries() {
        try {
            const countries = await this.apiRequest('/api/admin/countries');
            const container = document.getElementById('countries-content');

            const html = `
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Список стран</h5>
                        <button class="btn btn-primary" onclick="adminPanel.showCountryModal()">
                            <i class="bi bi-plus"></i> Добавить страну
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Код</th>
                                        <th>Название</th>
                                        <th>Активна</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${countries.map(country => `
                                        <tr>
                                            <td>${country.id}</td>
                                            <td><code>${country.code}</code></td>
                                            <td>${country.name}</td>
                                            <td><span class="badge bg-${country.is_active ? 'success' : 'secondary'}">${country.is_active ? 'Да' : 'Нет'}</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1" onclick="adminPanel.editCountry(${country.id})" title="Редактировать">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="adminPanel.deleteCountry(${country.id})" title="Удалить">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        } catch (error) {
            console.error('Ошибка загрузки стран:', error);
            this.showError('Ошибка загрузки стран');
        }
    }

    /**
     * Загрузка цен
     */
    async loadPrices() {
        try {
            const prices = await this.apiRequest('/api/admin/prices');
            const container = document.getElementById('prices-content');

            const html = `
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Управление ценами</h5>
                        <button class="btn btn-primary" onclick="adminPanel.showPriceModal()">
                            <i class="bi bi-plus"></i> Добавить цену
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Страна</th>
                                        <th>Сервис</th>
                                        <th>Цена клиента</th>
                                        <th>Доступно</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${prices.map(price => `
                                        <tr>
                                            <td>${price.id}</td>
                                            <td>
                                                ${price.country ? price.country.name : `ID: ${price.country_id}`}
                                                <small class="text-muted d-block">(${price.country ? price.country.code : 'N/A'})</small>
                                            </td>
                                            <td>
                                                ${price.service ? price.service.name : `ID: ${price.service_id}`}
                                                <small class="text-muted d-block">(${price.service ? price.service.code : 'N/A'})</small>
                                            </td>
                                            <td><strong>$${price.price}</strong></td>
                                            <td>${price.available || '∞'}</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1" onclick="adminPanel.editPrice(${price.id})" title="Редактировать">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="adminPanel.deletePrice(${price.id})" title="Удалить">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        } catch (error) {
            console.error('Ошибка загрузки цен:', error);
            this.showError('Ошибка загрузки цен');
        }
    }

    /**
     * Загрузка настроек
     */
    async loadSettings() {
        try {
            const settings = await this.apiRequest('/api/admin/settings');
            const container = document.getElementById('settings-content');

            const html = `
                <div class="card">
                    <div class="card-header">
                        <h5>Настройки системы</h5>
                    </div>
                    <div class="card-body">
                        <form id="settingsForm">
                            <!-- Основные системные настройки -->
                            <h6 class="mb-3"><i class="bi bi-sliders"></i> Системные настройки</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Время ожидания SMS (сек)</label>
                                        <input type="number" class="form-control" name="sms_timeout" value="${settings?.sms_timeout || 1200}" min="60" max="3600">
                                        <div class="form-text">Время ожидания SMS кода в секундах (60-3600 сек)</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Режим отладки</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="debug_mode" ${settings?.debug_mode ? 'checked' : ''}>
                                            <label class="form-check-label">Включить режим отладки</label>
                                        </div>
                                        <div class="form-text">Включает подробное логирование</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Уровень логирования</label>
                                        <select class="form-select" name="log_level">
                                            <option value="DEBUG" ${settings?.log_level === 'DEBUG' ? 'selected' : ''}>DEBUG</option>
                                            <option value="INFO" ${settings?.log_level === 'INFO' ? 'selected' : ''}>INFO</option>
                                            <option value="WARNING" ${settings?.log_level === 'WARNING' ? 'selected' : ''}>WARNING</option>
                                            <option value="ERROR" ${settings?.log_level === 'ERROR' ? 'selected' : ''}>ERROR</option>
                                            <option value="CRITICAL" ${settings?.log_level === 'CRITICAL' ? 'selected' : ''}>CRITICAL</option>
                                        </select>
                                        <div class="form-text">Минимальный уровень логирования</div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> Сохранить настройки
                                </button>
                                <div class="text-muted small">
                                    <i class="bi bi-info-circle"></i> Настройки будут сохранены в базу данных и файл .env
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            container.innerHTML = html;

            // Обработчик формы настроек
            document.getElementById('settingsForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.saveSettings(new FormData(e.target));
            });

        } catch (error) {
            console.error('Ошибка загрузки настроек:', error);
            this.showError('Ошибка загрузки настроек');
        }
    }

    /**
     * Сохранение настроек
     */
    async saveSettings(formData) {
        try {
            const data = Object.fromEntries(formData);

            // Обрабатываем чекбоксы (они не попадают в FormData если не отмечены)
            const form = document.getElementById('settingsForm');
            data.debug_mode = form.querySelector('input[name="debug_mode"]').checked;

            // Преобразуем sms_timeout в число
            if (data.sms_timeout) {
                data.sms_timeout = parseInt(data.sms_timeout);
            }

            console.log('Сохранение настроек:', data);

            await this.apiRequest('/api/admin/settings', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            this.showSuccess('Настройки сохранены успешно');

        } catch (error) {
            console.error('Ошибка сохранения настроек:', error);
            this.showError('Ошибка сохранения настроек');
        }
    }

    /**
     * Загрузка логов - УДАЛЕНО: теперь используется пагинация через logsPagination
     */

    /**
     * Загрузка активаций - УДАЛЕНО: теперь используется пагинация через activationsPagination
     */

    /**
     * Показать модальное окно для создания/редактирования сервиса
     */
    showServiceModal(serviceId = null) {
        const isEdit = serviceId !== null;
        const title = isEdit ? 'Редактировать сервис' : 'Добавить сервис';

        const modalHtml = `
            <div class="modal fade" id="serviceModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="serviceForm">
                                <div class="mb-3">
                                    <label class="form-label">Код сервиса *</label>
                                    <input type="text" class="form-control" name="code" required
                                           placeholder="Например: tg, wa, vk" maxlength="10">
                                    <div class="form-text">Уникальный код сервиса (до 10 символов)</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Название сервиса *</label>
                                    <input type="text" class="form-control" name="name" required
                                           placeholder="Например: Telegram" maxlength="100">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_active" checked>
                                        <label class="form-check-label">Активен</label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <button type="button" class="btn btn-primary" onclick="adminPanel.saveService(${serviceId})">
                                ${isEdit ? 'Сохранить' : 'Создать'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('serviceModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Если редактируем, загружаем данные
        if (isEdit) {
            this.loadServiceData(serviceId);
        }

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('serviceModal'));
        modal.show();
    }

    /**
     * Загрузить данные сервиса для редактирования
     */
    async loadServiceData(serviceId) {
        try {
            const services = await this.apiRequest('/api/admin/services');
            const service = services.find(s => s.id === serviceId);

            if (service) {
                const form = document.getElementById('serviceForm');
                form.code.value = service.code;
                form.name.value = service.name;
                form.is_active.checked = service.is_active;
            }
        } catch (error) {
            console.error('Ошибка загрузки данных сервиса:', error);
            this.showError('Ошибка загрузки данных сервиса');
        }
    }

    /**
     * Сохранить сервис
     */
    async saveService(serviceId = null) {
        try {
            const form = document.getElementById('serviceForm');
            const formData = new FormData(form);

            const data = {
                code: formData.get('code'),
                name: formData.get('name'),
                is_active: formData.has('is_active')
            };

            const isEdit = serviceId !== null;
            const url = isEdit ? `/api/admin/services/${serviceId}` : '/api/admin/services';
            const method = isEdit ? 'PUT' : 'POST';

            await this.apiRequest(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('serviceModal'));
            modal.hide();

            // Обновляем список сервисов через пагинацию
            await this.servicesPagination.refresh();

            this.showSuccess(isEdit ? 'Сервис успешно обновлен' : 'Сервис успешно создан');

        } catch (error) {
            console.error('Ошибка сохранения сервиса:', error);
            this.showError('Ошибка сохранения сервиса: ' + error.message);
        }
    }

    /**
     * Редактировать сервис
     */
    editService(serviceId) {
        this.showServiceModal(serviceId);
    }

    /**
     * Удаление сервиса
     */
    async deleteService(serviceId) {
        if (!confirm('Вы уверены, что хотите удалить этот сервис? Это действие нельзя отменить.\n\nВнимание: сервис можно удалить только если у него нет связанных цен или активаций.')) {
            return;
        }

        try {
            await this.apiRequest(`/api/admin/services/${serviceId}`, {
                method: 'DELETE'
            });

            this.showSuccess('Сервис успешно удален');

            // Обновляем список сервисов через пагинацию
            await this.servicesPagination.refresh();
        } catch (error) {
            console.error('Ошибка удаления сервиса:', error);

            // Показываем более детальную ошибку
            let errorMessage = 'Ошибка удаления сервиса';
            if (error.message && error.message.includes('цены')) {
                errorMessage = 'Нельзя удалить сервис, у которого есть цены. Сначала удалите все связанные цены.';
            } else if (error.message && error.message.includes('активации')) {
                errorMessage = 'Нельзя удалить сервис, у которого есть активации.';
            }

            this.showError(errorMessage);
        }
    }

    /**
     * Показать модальное окно для создания/редактирования страны
     */
    showCountryModal(countryId = null) {
        const isEdit = countryId !== null;
        const title = isEdit ? 'Редактировать страну' : 'Добавить страну';

        const modalHtml = `
            <div class="modal fade" id="countryModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="countryForm">
                                <div class="mb-3">
                                    <label class="form-label">Код страны *</label>
                                    <input type="number" class="form-control" name="code" required
                                           placeholder="Например: 0, 1, 7" min="0">
                                    <div class="form-text">Уникальный числовой код страны</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Название страны *</label>
                                    <input type="text" class="form-control" name="name" required
                                           placeholder="Например: Россия" maxlength="100">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_active" checked>
                                        <label class="form-check-label">Активна</label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <button type="button" class="btn btn-primary" onclick="adminPanel.saveCountry(${countryId})">
                                ${isEdit ? 'Сохранить' : 'Создать'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('countryModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Если редактируем, загружаем данные
        if (isEdit) {
            this.loadCountryData(countryId);
        }

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('countryModal'));
        modal.show();
    }

    /**
     * Загрузить данные страны для редактирования
     */
    async loadCountryData(countryId) {
        try {
            const countries = await this.apiRequest('/api/admin/countries');
            const country = countries.find(c => c.id === countryId);

            if (country) {
                const form = document.getElementById('countryForm');
                form.code.value = country.code;
                form.name.value = country.name;
                form.is_active.checked = country.is_active;
            }
        } catch (error) {
            console.error('Ошибка загрузки данных страны:', error);
            this.showError('Ошибка загрузки данных страны');
        }
    }

    /**
     * Сохранить страну
     */
    async saveCountry(countryId = null) {
        try {
            const form = document.getElementById('countryForm');
            const formData = new FormData(form);

            const data = {
                code: parseInt(formData.get('code')),
                name: formData.get('name'),
                is_active: formData.has('is_active')
            };

            const isEdit = countryId !== null;
            const url = isEdit ? `/api/admin/countries/${countryId}` : '/api/admin/countries';
            const method = isEdit ? 'PUT' : 'POST';

            await this.apiRequest(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('countryModal'));
            modal.hide();

            // Обновляем список стран
            await this.loadCountries();

            this.showSuccess(isEdit ? 'Страна успешно обновлена' : 'Страна успешно создана');

        } catch (error) {
            console.error('Ошибка сохранения страны:', error);
            this.showError('Ошибка сохранения страны: ' + error.message);
        }
    }

    /**
     * Редактировать страну
     */
    editCountry(countryId) {
        this.showCountryModal(countryId);
    }

    /**
     * Удаление страны
     */
    async deleteCountry(countryId) {
        if (!confirm('Вы уверены, что хотите удалить эту страну? Это действие нельзя отменить.\n\nВнимание: страну можно удалить только если у неё нет связанных цен или активаций.')) {
            return;
        }

        try {
            await this.apiRequest(`/api/admin/countries/${countryId}`, {
                method: 'DELETE'
            });

            this.showSuccess('Страна успешно удалена');

            // Обновляем список стран
            await this.loadCountries();
        } catch (error) {
            console.error('Ошибка удаления страны:', error);

            // Показываем более детальную ошибку
            let errorMessage = 'Ошибка удаления страны';
            if (error.message && error.message.includes('цены')) {
                errorMessage = 'Нельзя удалить страну, у которой есть цены. Сначала удалите все связанные цены.';
            } else if (error.message && error.message.includes('активации')) {
                errorMessage = 'Нельзя удалить страну, у которой есть активации.';
            }

            this.showError(errorMessage);
        }
    }

    /**
     * Показать модальное окно для создания/редактирования цены
     */
    async showPriceModal(priceId = null) {
        const isEdit = priceId !== null;
        const title = isEdit ? 'Редактировать цену' : 'Добавить цену';

        try {
            // Загружаем списки стран и сервисов для выпадающих списков
            const [countries, services] = await Promise.all([
                this.apiRequest('/api/admin/countries'),
                this.apiRequest('/api/admin/services')
            ]);

            const modalHtml = `
                <div class="modal fade" id="priceModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="priceForm">
                                    <div class="mb-3">
                                        <label class="form-label">Страна *</label>
                                        <select class="form-select" name="country_id" required>
                                            <option value="">Выберите страну</option>
                                            ${countries.map(country => `
                                                <option value="${country.id}">${country.name} (${country.code})</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Сервис *</label>
                                        <select class="form-select" name="service_id" required>
                                            <option value="">Выберите сервис</option>
                                            ${services.map(service => `
                                                <option value="${service.id}">${service.name} (${service.code})</option>
                                            `).join('')}
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Цена</label>
                                        <input type="number" class="form-control" name="price"
                                               step="0.001" min="0" placeholder="0.000" required>
                                        <div class="form-text">Цена в долларах США</div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Доступное количество</label>
                                        <input type="number" class="form-control" name="available"
                                               min="0" placeholder="Не ограничено">
                                        <div class="form-text">Количество доступных номеров (опционально)</div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                                <button type="button" class="btn btn-primary" onclick="adminPanel.savePrice(${priceId})">
                                    ${isEdit ? 'Сохранить' : 'Создать'}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Удаляем предыдущее модальное окно если есть
            const existingModal = document.getElementById('priceModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Добавляем новое модальное окно
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Если редактируем, загружаем данные
            if (isEdit) {
                await this.loadPriceData(priceId);
            }

            // Показываем модальное окно
            const modal = new bootstrap.Modal(document.getElementById('priceModal'));
            modal.show();

        } catch (error) {
            console.error('Ошибка загрузки данных для модального окна цены:', error);
            this.showError('Ошибка загрузки данных');
        }
    }

    /**
     * Загрузить данные цены для редактирования
     */
    async loadPriceData(priceId) {
        try {
            const prices = await this.apiRequest('/api/admin/prices');
            const price = prices.find(p => p.id === priceId);

            if (price) {
                const form = document.getElementById('priceForm');
                form.country_id.value = price.country_id;
                form.service_id.value = price.service_id;
                form.price.value = price.price;
                form.available.value = price.available || '';
            }
        } catch (error) {
            console.error('Ошибка загрузки данных цены:', error);
            this.showError('Ошибка загрузки данных цены');
        }
    }

    /**
     * Сохранить цену
     */
    async savePrice(priceId = null) {
        try {
            const form = document.getElementById('priceForm');
            const formData = new FormData(form);

            const data = {
                country_id: parseInt(formData.get('country_id')),
                service_id: parseInt(formData.get('service_id')),
                price: parseFloat(formData.get('price'))
            };

            // Добавляем опциональное поле available если оно заполнено
            const available = formData.get('available');
            if (available) {
                data.available = parseInt(available);
            }

            const isEdit = priceId !== null;
            const url = isEdit ? `/api/admin/prices/${priceId}` : '/api/admin/prices';
            const method = isEdit ? 'PUT' : 'POST';

            await this.apiRequest(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('priceModal'));
            modal.hide();

            // Обновляем список цен
            await this.loadPrices();

            this.showSuccess(isEdit ? 'Цена успешно обновлена' : 'Цена успешно создана');

        } catch (error) {
            console.error('Ошибка сохранения цены:', error);
            this.showError('Ошибка сохранения цены: ' + error.message);
        }
    }

    /**
     * Редактировать цену
     */
    editPrice(priceId) {
        this.showPriceModal(priceId);
    }

    /**
     * Удаление цены
     */
    async deletePrice(priceId) {
        if (!confirm('Вы уверены, что хотите удалить эту цену? Это действие нельзя отменить.')) {
            return;
        }

        try {
            await this.apiRequest(`/api/admin/prices/${priceId}`, {
                method: 'DELETE'
            });

            this.showSuccess('Цена успешно удалена');

            // Обновляем список цен
            await this.loadPrices();
        } catch (error) {
            console.error('Ошибка удаления цены:', error);
            this.showError('Ошибка удаления цены');
        }
    }

    /**
     * ===============================================
     * МЕТОДЫ УПРАВЛЕНИЯ ПРОВАЙДЕРАМИ
     * ===============================================
     */

    /**
     * Загрузить список провайдеров
     */
    async loadProviders() {
        try {
            console.log('Загрузка провайдеров...');
            const response = await this.apiRequest('/api/admin/providers');
            console.log('Ответ API провайдеров:', response);

            // API возвращает объект с полем providers
            const providers = response.providers || [];
            console.log('Провайдеры загружены:', providers);

            const container = document.getElementById('providers-content');
            if (!container) return;

            // Убираем индикатор загрузки
            container.classList.remove('loading');

            if (providers.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="bi bi-hdd-network fs-1 text-muted"></i>
                        <p class="text-muted mt-2">Провайдеры не найдены</p>
                        <button class="btn btn-primary" onclick="adminPanel.showProviderModal()">
                            <i class="bi bi-plus-circle"></i> Добавить первого провайдера
                        </button>
                    </div>
                `;
                return;
            }

            // Создаем таблицу провайдеров
            const tableHtml = `
                <div class="table-responsive">
                    <table class="table table-hover align-middle">
                        <thead class="table-dark">
                            <tr>
                                <th style="width: 40px;">
                                    <i class="bi bi-list-ol"></i>
                                </th>
                                <th>Название</th>
                                <th>URL</th>
                                <th>Тип API</th>
                                <th>Статус</th>
                                <th>Приоритет</th>
                                <th>Последний тест</th>
                                <th style="width: 200px;">Действия</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${providers.map((provider, index) => `
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">${provider.priority}</span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>${provider.display_name}</strong>
                                            <br>
                                            <small class="text-muted">${provider.name}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">${provider.api_url}</small>
                                    </td>
                                    <td>
                                        <span class="badge ${provider.api_format === 'firefox_api' ? 'bg-warning' : 'bg-info'}">
                                            ${provider.api_format === 'firefox_api' ? 'Firefox' : 'SMSActivate'}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox"
                                                   ${provider.is_active ? 'checked' : ''}
                                                   onchange="adminPanel.toggleProvider(${provider.id}, this.checked)">
                                            <label class="form-check-label">
                                                <span class="badge ${provider.is_active ? 'bg-success' : 'bg-secondary'}">
                                                    ${provider.is_active ? 'Активен' : 'Отключен'}
                                                </span>
                                            </label>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="input-group input-group-sm" style="width: 100px;">
                                            <input type="number" class="form-control" value="${provider.priority}"
                                                   min="1" max="1000"
                                                   onchange="adminPanel.updatePriority(${provider.id}, this.value)">
                                        </div>
                                    </td>
                                    <td>
                                        ${provider.last_test_at ?
                                            `<div>
                                                <span class="badge ${provider.last_test_result === 'SUCCESS' ? 'bg-success' : 'bg-danger'}">
                                                    ${provider.last_test_result}
                                                </span>
                                                <br>
                                                <small class="text-muted">${this.formatDate(provider.last_test_at)}</small>
                                            </div>`
                                            : '<span class="text-muted">Не тестировался</span>'
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-info" title="Тест API"
                                                    onclick="adminPanel.testProvider(${provider.id})">
                                                <i class="bi bi-shield-check"></i>
                                            </button>
                                            <button class="btn btn-outline-primary" title="Редактировать"
                                                    onclick="adminPanel.editProvider(${provider.id})">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="Удалить"
                                                    onclick="adminPanel.deleteProvider(${provider.id})">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = tableHtml;
            console.log('Таблица провайдеров создана');

        } catch (error) {
            console.error('Ошибка загрузки провайдеров:', error);
            const container = document.getElementById('providers-content');
            if (container) {
                container.classList.remove('loading');
                container.innerHTML = '<p class="text-danger">Ошибка загрузки провайдеров</p>';
            }
        }
    }

    /**
     * Показать модальное окно провайдера (создание/редактирование)
     */
    showProviderModal(providerId = null) {
        console.log('Показ модального окна провайдера:', providerId);

        const isEdit = providerId !== null;
        const modal = document.getElementById('providerModal');
        const modalTitle = document.getElementById('provider-modal-title');

        // Устанавливаем заголовок
        modalTitle.textContent = isEdit ? 'Редактировать провайдер' : 'Добавить провайдер';

        // Сбрасываем форму
        const form = document.getElementById('providerForm');
        form.reset();

        // Скрываем все специфичные поля
        document.getElementById('smsactivate-fields').style.display = 'none';
        document.getElementById('firefox-fields').style.display = 'none';

        // Если редактируем, загружаем данные
        if (isEdit) {
            this.loadProviderData(providerId);
        }

        // Показываем модальное окно
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }

    /**
     * Загрузить данные провайдера для редактирования
     */
    async loadProviderData(providerId) {
        try {
            const provider = await this.apiRequest(`/api/admin/providers/${providerId}`);
            console.log('Данные провайдера загружены:', provider);

            const form = document.getElementById('providerForm');

            // Заполняем основные поля
            form.name.value = provider.name;
            form.display_name.value = provider.display_name;
            form.api_url.value = provider.api_url;
            form.api_format.value = provider.api_format;
            form.priority.value = provider.priority;
            form.timeout_seconds.value = provider.timeout_seconds;
            form.max_requests_per_minute.value = provider.max_requests_per_minute;
            form.is_active.checked = provider.is_active;

            // Устанавливаем скрытое поле ID
            document.getElementById('provider-id').value = providerId;

            // Показываем соответствующие поля и заполняем их
            this.onApiFormatChange(provider.api_format);

            if (provider.api_format === 'smsactivate_api') {
                // Заполняем API ключ для SMSActivate
                const apiKeyInput = document.getElementById('provider-api-key');
                if (apiKeyInput) {
                    apiKeyInput.value = provider.api_key || '';
                }
            } else if (provider.api_format === 'firefox_api') {
                // Заполняем логин и пароль для Firefox API
                const usernameInput = document.getElementById('provider-username');
                const passwordInput = document.getElementById('provider-password');
                if (usernameInput) {
                    usernameInput.value = provider.username || '';
                }
                if (passwordInput) {
                    passwordInput.value = provider.password || '';
                }
            }

        } catch (error) {
            console.error('Ошибка загрузки данных провайдера:', error);
            this.showError('Ошибка загрузки данных провайдера');
        }
    }

    /**
     * Переключение полей формы в зависимости от типа API
     */
    onApiFormatChange(apiFormat) {
        console.log('Смена типа API:', apiFormat);

        const smsactivateFields = document.getElementById('smsactivate-fields');
        const firefoxFields = document.getElementById('firefox-fields');
        const apiKeyInput = document.getElementById('provider-api-key');
        const usernameInput = document.getElementById('provider-username');
        const passwordInput = document.getElementById('provider-password');

        // Сбрасываем все required атрибуты
        apiKeyInput.required = false;
        usernameInput.required = false;
        passwordInput.required = false;

        if (apiFormat === 'smsactivate_api') {
            smsactivateFields.style.display = 'block';
            firefoxFields.style.display = 'none';
            apiKeyInput.required = true;
        } else if (apiFormat === 'firefox_api') {
            smsactivateFields.style.display = 'none';
            firefoxFields.style.display = 'block';
            usernameInput.required = true;
            passwordInput.required = true;
        } else {
            smsactivateFields.style.display = 'none';
            firefoxFields.style.display = 'none';
        }
    }

    /**
     * Сохранить провайдер
     */
    async saveProvider() {
        try {
            const form = document.getElementById('providerForm');
            const formData = new FormData(form);

            // Проверяем заполненность обязательных полей
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const providerId = document.getElementById('provider-id').value;
            const isEdit = providerId !== '';

            const data = {
                name: formData.get('name'),
                display_name: formData.get('display_name'),
                api_url: formData.get('api_url'),
                api_format: formData.get('api_format'),
                priority: parseInt(formData.get('priority')),
                timeout_seconds: parseInt(formData.get('timeout_seconds')),
                max_requests_per_minute: parseInt(formData.get('max_requests_per_minute')),
                is_active: formData.get('is_active') === 'on'
            };

            // Добавляем специфичные для типа API поля
            if (data.api_format === 'smsactivate_api') {
                data.api_key = formData.get('api_key');
            } else if (data.api_format === 'firefox_api') {
                data.username = formData.get('username');
                data.password = formData.get('password');
            }

            console.log('Сохранение провайдера:', data);

            const url = isEdit ? `/api/admin/providers/${providerId}` : '/api/admin/providers';
            const method = isEdit ? 'PUT' : 'POST';

            await this.apiRequest(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('providerModal'));
            modal.hide();

            // Обновляем список провайдеров
            await this.loadProviders();

            this.showSuccess(isEdit ? 'Провайдер успешно обновлен' : 'Провайдер успешно создан');

        } catch (error) {
            console.error('Ошибка сохранения провайдера:', error);
            this.showError('Ошибка сохранения провайдера: ' + error.message);
        }
    }

    /**
     * Редактировать провайдер
     */
    editProvider(providerId) {
        this.showProviderModal(providerId);
    }

    /**
     * Удалить провайдер
     */
    async deleteProvider(providerId) {
        if (!confirm('Вы уверены, что хотите удалить этого провайдера? Это действие нельзя отменить.')) {
            return;
        }

        try {
            await this.apiRequest(`/api/admin/providers/${providerId}`, {
                method: 'DELETE'
            });

            this.showSuccess('Провайдер успешно удален');
            await this.loadProviders();
        } catch (error) {
            console.error('Ошибка удаления провайдера:', error);
            this.showError('Ошибка удаления провайдера: ' + error.message);
        }
    }

    /**
     * Переключить активность провайдера
     */
    async toggleProvider(providerId, isActive) {
        // Отключаем переключатель на время выполнения запроса
        const checkbox = this.getProviderCheckbox(providerId);
        if (checkbox) {
            checkbox.disabled = true;
            this.addLoadingSpinner(checkbox);
        }

        try {
            await this.apiRequest(`/api/admin/providers/${providerId}/toggle`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ is_active: isActive })
            });

            // Обновляем визуальное состояние сразу после успешного API запроса
            this.updateProviderVisualState(providerId, isActive);

            this.showSuccess(`Провайдер ${isActive ? 'активирован' : 'отключен'}`);

        } catch (error) {
            console.error('Ошибка переключения провайдера:', error);
            this.showError('Ошибка переключения провайдера');
            // Возвращаем переключатель в исходное состояние при ошибке
            this.revertProviderToggle(providerId, !isActive);
            // Перезагружаем чтобы вернуть правильное состояние
            await this.loadProviders();
        } finally {
            // Включаем переключатель обратно и убираем спиннер
            if (checkbox) {
                checkbox.disabled = false;
                this.removeLoadingSpinner(checkbox);
            }
        }
    }

    /**
     * Получить checkbox провайдера по ID
     */
    getProviderCheckbox(providerId) {
        const table = document.querySelector('#providers-content table tbody');
        if (!table) return null;
        return table.querySelector(`input[onchange*="toggleProvider(${providerId}"]`);
    }

    /**
     * Добавить индикатор загрузки рядом с checkbox
     */
    addLoadingSpinner(checkbox) {
        if (!checkbox || checkbox.parentElement.querySelector('.loading-spinner')) return;

        const spinner = document.createElement('div');
        spinner.className = 'loading-spinner spinner-border spinner-border-sm text-primary ms-2';
        spinner.style.width = '16px';
        spinner.style.height = '16px';
        spinner.setAttribute('role', 'status');

        checkbox.parentElement.appendChild(spinner);
    }

    /**
     * Убрать индикатор загрузки
     */
    removeLoadingSpinner(checkbox) {
        if (!checkbox) return;

        const spinner = checkbox.parentElement.querySelector('.loading-spinner');
        if (spinner) {
            spinner.remove();
        }
    }

    /**
     * Обновить визуальное состояние провайдера
     */
    updateProviderVisualState(providerId, isActive) {
        // Находим checkbox провайдера
        const checkbox = this.getProviderCheckbox(providerId);
        if (!checkbox) return;

        // Обновляем состояние checkbox
        checkbox.checked = isActive;

        // Находим badge рядом с checkbox
        const badge = checkbox.parentElement.querySelector('.badge');
        if (badge) {
            // Обновляем класс и текст badge
            badge.className = `badge ${isActive ? 'bg-success' : 'bg-secondary'}`;
            badge.textContent = isActive ? 'Активен' : 'Отключен';
        }
    }

    /**
     * Вернуть переключатель в исходное состояние при ошибке
     */
    revertProviderToggle(providerId, originalState) {
        const checkbox = this.getProviderCheckbox(providerId);
        if (checkbox) {
            checkbox.checked = originalState;
        }
    }

    /**
     * Обновить приоритет провайдера
     */
    async updatePriority(providerId, priority) {
        try {
            await this.apiRequest(`/api/admin/providers/${providerId}/priority`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ priority: parseInt(priority) })
            });

            this.showSuccess('Приоритет провайдера обновлен');

        } catch (error) {
            console.error('Ошибка обновления приоритета:', error);
            this.showError('Ошибка обновления приоритета');
            await this.loadProviders();
        }
    }

    /**
     * Тестировать провайдер
     */
    async testProvider(providerId) {
        try {
            this.showAlert('Тестирование провайдера...', 'info');

            const result = await this.apiRequest(`/api/admin/providers/${providerId}/test`, {
                method: 'POST'
            });

            if (result.success) {
                this.showSuccess(`Тест успешен: ${result.message}`);
            } else {
                this.showError(`Тест провалился: ${result.message}`);
            }

            // Обновляем список провайдеров чтобы показать результаты теста
            await this.loadProviders();

        } catch (error) {
            console.error('Ошибка тестирования провайдера:', error);
            this.showError('Ошибка тестирования провайдера: ' + error.message);
        }
    }

    /**
     * Тестирование соединения при добавлении провайдера
     */
    async testProviderConnection() {
        try {
            const form = document.getElementById('providerForm');
            const formData = new FormData(form);

            // Проверяем заполненность обязательных полей
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const data = {
                api_url: formData.get('api_url'),
                api_format: formData.get('api_format'),
                timeout_seconds: parseInt(formData.get('timeout_seconds')) || 30
            };

            // Добавляем учетные данные
            if (data.api_format === 'smsactivate_api') {
                data.api_key = formData.get('api_key');
            } else if (data.api_format === 'firefox_api') {
                data.username = formData.get('username');
                data.password = formData.get('password');
            }

            this.showAlert('Тестирование соединения...', 'info');

            const result = await this.apiRequest('/api/admin/providers/test-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            if (result.success) {
                this.showSuccess(`Соединение успешно: ${result.message}`);
            } else {
                this.showError(`Соединение не удалось: ${result.message}`);
            }

        } catch (error) {
            console.error('Ошибка тестирования соединения:', error);
            this.showError('Ошибка тестирования соединения: ' + error.message);
        }
    }

    /**
     * Тестировать все провайдеры
     */
    async testAllProviders() {
        try {
            this.showAlert('Тестирование всех провайдеров...', 'info');

            const response = await this.apiRequest('/api/admin/providers/test-all', {
                method: 'POST'
            });

            // Проверяем, что response содержит поле results
            if (!response || !response.results) {
                throw new Error('Некорректный ответ от сервера: отсутствует поле results');
            }

            const results = response.results;
            let successCount = 0;
            let failCount = 0;

            // Проверяем, что results является массивом
            if (!Array.isArray(results)) {
                throw new Error('Ответ сервера содержит некорректные данные: results не является массивом');
            }

            results.forEach(result => {
                if (result.success) successCount++;
                else failCount++;
            });

            // Показываем детальные результаты в модальном окне
            this.showTestResultsModal(results, response.summary);

            this.showAlert(
                `Тестирование завершено: ${successCount} успешно, ${failCount} провалились`,
                successCount > failCount ? 'success' : 'warning'
            );

            // Обновляем список провайдеров
            await this.loadProviders();

        } catch (error) {
            console.error('Ошибка тестирования провайдеров:', error);
            this.showError('Ошибка тестирования провайдеров: ' + error.message);
        }
    }

    /**
     * Показать модальное окно с результатами тестирования
     */
    showTestResultsModal(results, summary) {
        const modalHtml = `
            <div class="modal fade" id="testResultsModal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-shield-check"></i> Результаты тестирования провайдеров
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Общая статистика -->
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-primary">${summary?.total || 0}</h5>
                                            <p class="card-text">Всего провайдеров</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-success">${summary?.successful || 0}</h5>
                                            <p class="card-text">Успешных</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <h5 class="card-title text-danger">${summary?.failed || 0}</h5>
                                            <p class="card-text">Провалившихся</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Детальные результаты -->
                            <div class="accordion" id="testResultsAccordion">
                                ${results.map((result, index) => `
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="heading${index}">
                                            <button class="accordion-button ${result.success ? 'text-success' : 'text-danger'}"
                                                    type="button" data-bs-toggle="collapse"
                                                    data-bs-target="#collapse${index}">
                                                <div class="d-flex justify-content-between w-100 me-3">
                                                    <div>
                                                        <i class="bi bi-${result.success ? 'check-circle-fill' : 'x-circle-fill'}"></i>
                                                        <strong>${result.provider_name}</strong>
                                                        <small class="text-muted">(ID: ${result.provider_id})</small>
                                                    </div>
                                                    <div>
                                                        <span class="badge ${result.success ? 'bg-success' : 'bg-danger'}">
                                                            ${result.success ? 'УСПЕХ' : 'ОШИБКА'}
                                                        </span>
                                                        ${result.response_time > 0 ?
                                                            `<small class="text-muted ms-2">${result.response_time}с</small>` :
                                                            ''
                                                        }
                                                    </div>
                                                </div>
                                            </button>
                                        </h2>
                                        <div id="collapse${index}" class="accordion-collapse collapse"
                                             data-bs-parent="#testResultsAccordion">
                                            <div class="accordion-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <strong>Статус:</strong>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <span class="badge ${result.success ? 'bg-success' : 'bg-danger'}">
                                                            ${result.success ? 'Успешно' : 'Провалено'}
                                                        </span>
                                                    </div>
                                                </div>

                                                ${result.response_time > 0 ? `
                                                <div class="row mt-2">
                                                    <div class="col-md-3">
                                                        <strong>Время ответа:</strong>
                                                    </div>
                                                    <div class="col-md-9">
                                                        ${result.response_time} секунд
                                                    </div>
                                                </div>
                                                ` : ''}

                                                <div class="row mt-2">
                                                    <div class="col-md-3">
                                                        <strong>Сообщение:</strong>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <div class="alert alert-${result.success ? 'success' : 'danger'} mb-0">
                                                            <pre class="mb-0" style="white-space: pre-wrap; font-size: 0.9em;">${result.message}</pre>
                                                        </div>
                                                    </div>
                                                </div>

                                                ${result.api_response ? `
                                                <div class="row mt-3">
                                                    <div class="col-md-3">
                                                        <strong>Ответ провайдера:</strong>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <div class="alert alert-info mb-0">
                                                            <pre class="mb-0" style="white-space: pre-wrap; font-size: 0.8em; max-height: 200px; overflow-y: auto;">${typeof result.api_response === 'object' ? JSON.stringify(result.api_response, null, 2) : result.api_response}</pre>
                                                        </div>
                                                    </div>
                                                </div>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрыть</button>
                            <button type="button" class="btn btn-primary" onclick="adminPanel.testAllProviders()">
                                <i class="bi bi-arrow-clockwise"></i> Повторить тестирование
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('testResultsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('testResultsModal'));
        modal.show();
    }

    /**
     * Перезагрузить кеш провайдеров
     */
    async reloadProviders() {
        try {
            await this.apiRequest('/api/admin/providers/reload', {
                method: 'POST'
            });

            this.showSuccess('Кеш провайдеров перезагружен');
            await this.loadProviders();

        } catch (error) {
            console.error('Ошибка перезагрузки провайдеров:', error);
            this.showError('Ошибка перезагрузки провайдеров');
        }
    }

    /**
     * API запрос с автоматическим определением базового URL
     */
    async apiRequest(url, options = {}) {
        console.log('API Request:', url, 'API Key:', this.apiKey ? `Present (${this.apiKey.substring(0, 10)}...)` : 'Missing');

        // Убеждаемся, что API ключ актуальный из localStorage
        this.apiKey = localStorage.getItem('admin_api_key') || '';

        // Формируем правильный URL с учетом текущего протокола и домена
        let fullUrl = url;
        if (url.startsWith('/')) {
            // Если URL относительный, строим полный URL на основе текущего location
            const protocol = window.location.protocol; // 'http:' или 'https:'
            const host = window.location.host; // 'cock-liz.com' или 'IP:port'
            fullUrl = `${protocol}//${host}${url}`;
        }

        console.log('Full API URL:', fullUrl);

        // Правильно объединяем заголовки
        const headers = {
            'X-API-Key': this.apiKey,
            'Content-Type': 'application/json'
        };

        // Добавляем дополнительные заголовки из options
        if (options.headers) {
            Object.assign(headers, options.headers);
        }

        const requestOptions = {
            ...options,
            headers: headers
        };

        console.log('Request headers:', headers);
        const response = await fetch(fullUrl, requestOptions);

        console.log('API Response:', response.status, response.statusText);

        if (response.status === 401) {
            console.log('Unauthorized - using main auth form');
            // Сохраняем прерванный запрос
            this.pendingRequest = { url, options };
            // Используем основную форму авторизации вместо модального окна
            // Перенаправляем на основную форму авторизации
            localStorage.removeItem('admin_api_key');
            this.apiKey = '';
            location.reload(); // Перезагружаем страницу для показа основной формы авторизации
            throw new Error('Не авторизован');
        }

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error:', response.status, errorText);
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * Показать модальное окно входа - ОТКЛЮЧЕНО, используем основную форму авторизации
     */
    showLoginModal() {
        // Отключено - используем основную форму авторизации из HTML
        console.log('Используем основную форму авторизации вместо модального окна');
        return;

        // Проверяем, не открыто ли уже модальное окно
        if (document.getElementById('loginModal')) {
            return;
        }

        const modalHtml = `
            <div class="modal fade" id="loginModal" tabindex="-1" data-bs-backdrop="static">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Вход в админ панель</h5>
                        </div>
                        <div class="modal-body">
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label class="form-label">API ключ</label>
                                    <input type="password" class="form-control" id="apiKeyInput" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">Войти</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('loginModal'));
        modal.show();

        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.login();
        });
    }

    /**
     * Вход в систему
     */
    async login() {
        const apiKey = document.getElementById('apiKeyInput').value;
        console.log('Attempting login with API key:', apiKey);

        try {
            // Проверяем API ключ
            const response = await fetch('/api/admin/statistics', {
                headers: {
                    'X-API-Key': apiKey
                }
            });

            console.log('Login response:', response.status, response.statusText);

            if (response.ok) {
                this.apiKey = apiKey;
                localStorage.setItem('admin_api_key', apiKey);
                console.log('Login successful, API key saved to localStorage');

                // Закрываем модальное окно
                const modal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
                modal.hide();
                document.getElementById('loginModal').remove();

                // Если есть прерванный запрос, выполняем его
                if (this.pendingRequest) {
                    console.log('Executing pending request:', this.pendingRequest.url);
                    try {
                        const result = await this.apiRequest(this.pendingRequest.url, this.pendingRequest.options);
                        this.showSuccess('Операция выполнена успешно');

                        // Если это был запрос пополнения баланса, обновляем список пользователей
                        if (this.pendingRequest.url.includes('/topup')) {
                            this.loadUsers();
                        }
                    } catch (error) {
                        console.error('Error executing pending request:', error);
                        this.showError('Ошибка выполнения операции');
                    }
                    this.pendingRequest = null;
                } else {
                    // Загружаем дашборд только если нет прерванного запроса
                    this.loadDashboard();
                    this.showSuccess('Успешный вход в систему');
                }
            } else {
                const errorText = await response.text();
                console.error('Login failed:', response.status, errorText);
                this.showError('Неверный API ключ');
            }
        } catch (error) {
            console.error('Ошибка входа:', error);
            this.showError('Ошибка подключения к серверу');
        }
    }

    /**
     * Выход из системы
     */
    logout() {
        this.apiKey = '';
        localStorage.removeItem('admin_api_key');
        location.reload();
    }

    /**
     * Показать ошибку
     */
    showError(message) {
        this.showAlert(message, 'danger');
    }

    /**
     * Показать успех
     */
    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    /**
     * Показать уведомление
     */
    showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // Автоматически скрываем только для success и info, НЕ для ошибок
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                const alert = document.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
    }

    /**
     * Получить цвет статуса
     */
    getStatusColor(status) {
        const colors = {
            'PENDING': 'warning',
            'ACTIVE': 'primary',
            'COMPLETED': 'success',
            'CANCELLED': 'danger',
            'EXPIRED': 'secondary'
        };
        return colors[status] || 'secondary';
    }

    /**
     * Форматирование даты
     */
    formatDate(dateString) {
        if (!dateString) return '-';

        try {
            const date = new Date(dateString);

            // Проверяем, что дата валидна
            if (isNaN(date.getTime())) {
                return 'Неверная дата';
            }

            // Форматируем дату в читаемом виде
            return date.toLocaleString('ru-RU', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            console.error('Ошибка форматирования даты:', error, 'Дата:', dateString);
            return 'Ошибка даты';
        }
    }

    /**
     * Копировать текст в буфер обмена
     */
    copyToClipboard(inputId) {
        const input = document.getElementById(inputId);
        input.select();
        input.setSelectionRange(0, 99999); // Для мобильных устройств

        try {
            document.execCommand('copy');
            this.showSuccess('Скопировано в буфер обмена');
        } catch (err) {
            console.error('Ошибка копирования:', err);
            this.showError('Ошибка копирования в буфер обмена');
        }
    }

    /**
     * Переключение видимости пароля
     */
    togglePasswordVisibility(inputId, button) {
        const input = document.getElementById(inputId);
        const icon = button.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'bi bi-eye-slash';
            button.title = 'Скрыть';
        } else {
            input.type = 'password';
            icon.className = 'bi bi-eye';
            button.title = 'Показать';
        }
    }

    /**
     * Показать API ключ пользователя
     */
    async showApiKey(userId) {
        try {
            // Получаем данные пользователя с API ключом
            const response = await this.apiRequest(`/api/admin/users/${userId}/api-key`);

            // Создаем модальное окно для показа API ключа
            const modalHtml = `
                <div class="modal fade" id="apiKeyModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-key"></i> API ключ пользователя
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">Пользователь:</label>
                                    <p class="fw-bold">${response.username || 'Не указан'} (ID: ${response.user_id || userId})</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Email:</label>
                                    <p>${response.email || 'Не указан'}</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Роль:</label>
                                    <span class="badge bg-${response.role === 'admin' ? 'danger' : 'primary'}">${response.role || 'Не указана'}</span>
                                </div>
                                <div class="mb-3">
                                    <label for="apiKeyValue" class="form-label">API ключ:</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="apiKeyValue" value="${response.api_key || ''}" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="adminPanel.copyToClipboard('apiKeyValue')">
                                            <i class="bi bi-clipboard"></i> Копировать
                                        </button>
                                    </div>
                                </div>
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    Этот ключ используется для API запросов. Храните его в безопасности.
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрыть</button>
                                <button type="button" class="btn btn-warning" onclick="adminPanel.regenerateApiKey(${response.user_id || userId})">
                                    <i class="bi bi-arrow-clockwise"></i> Обновить ключ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Удаляем старое модальное окно если есть
            const existingModal = document.getElementById('apiKeyModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Добавляем новое модальное окно
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Показываем модальное окно
            const modal = new bootstrap.Modal(document.getElementById('apiKeyModal'));
            modal.show();

        } catch (error) {
            console.error('Ошибка получения API ключа:', error);
            this.showError('Ошибка получения API ключа: ' + error.message);
        }
    }

    /**
     * Обновить API ключ пользователя
     */
    async regenerateApiKey(userId) {
        try {
            const confirmed = confirm('Вы уверены, что хотите обновить API ключ? Старый ключ перестанет работать.');
            if (!confirmed) return;

            const response = await this.apiRequest(`/api/admin/users/${userId}/regenerate-api-key`, {
                method: 'POST'
            });

            this.showSuccess('API ключ успешно обновлен');

            // Обновляем модальное окно если оно открыто
            const apiKeyInput = document.getElementById('apiKeyValue');
            if (apiKeyInput) {
                apiKeyInput.value = response.new_api_key;
            }

            // Перезагружаем список пользователей
            this.loadUsers();

        } catch (error) {
            console.error('Ошибка обновления API ключа:', error);
            this.showError('Ошибка обновления API ключа: ' + error.message);
        }
    }

    /**
     * Редактировать пользователя
     */
    async editUser(userId) {
        try {
            // Получаем данные пользователя
            const user = await this.apiRequest(`/api/admin/users/${userId}`);

            // Показываем модальное окно редактирования
            this.showUserModal(user);

        } catch (error) {
            console.error('Ошибка загрузки данных пользователя:', error);
            this.showError('Ошибка загрузки данных пользователя: ' + error.message);
        }
    }

    /**
     * Пополнить баланс пользователя
     */
    async topupUser(userId) {
        try {
            const amount = prompt('Введите сумму для пополнения (в долларах):');
            if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
                this.showError('Введите корректную сумму');
                return;
            }

            const comment = prompt('Комментарий к транзакции (необязательно):') || 'Пополнение баланса админом';

            await this.apiRequest(`/api/admin/users/${userId}/topup`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    amount: parseFloat(amount),
                    comment: comment
                })
            });

            this.showSuccess(`Баланс пользователя пополнен на $${amount}`);
            this.loadUsers(); // Перезагружаем список пользователей

        } catch (error) {
            console.error('Ошибка пополнения баланса:', error);
            this.showError('Ошибка пополнения баланса: ' + error.message);
        }
    }

    /**
     * Списать с баланса пользователя
     */
    async deductUser(userId) {
        try {
            const amount = prompt('Введите сумму для списания (в долларах):');
            if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
                this.showError('Введите корректную сумму');
                return;
            }

            const comment = prompt('Комментарий к транзакции (необязательно):') || 'Списание баланса админом';

            await this.apiRequest(`/api/admin/users/${userId}/deduct`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    amount: parseFloat(amount),
                    comment: comment
                })
            });

            this.showSuccess(`С баланса пользователя списано $${amount}`);
            this.loadUsers(); // Перезагружаем список пользователей

        } catch (error) {
            console.error('Ошибка списания с баланса:', error);
            this.showError('Ошибка списания с баланса: ' + error.message);
        }
    }

    /**
     * Показать модальное окно создания/редактирования пользователя
     */
    showUserModal(user = null) {
        const isEdit = user !== null;
        const modalTitle = isEdit ? 'Редактировать пользователя' : 'Добавить пользователя';

        const modalHtml = `
            <div class="modal fade" id="userModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-person"></i> ${modalTitle}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="userForm">
                                <div class="mb-3">
                                    <label for="userUsername" class="form-label">Имя пользователя *</label>
                                    <input type="text" class="form-control" id="userUsername" name="username"
                                           value="${user ? user.username : ''}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="userEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="userEmail" name="email"
                                           value="${user ? user.email || '' : ''}">
                                </div>
                                <div class="mb-3">
                                    <label for="userRole" class="form-label">Роль *</label>
                                    <select class="form-select" id="userRole" name="role" required>
                                        <option value="user" ${user && user.role === 'user' ? 'selected' : ''}>Пользователь</option>
                                        <option value="admin" ${user && user.role === 'admin' ? 'selected' : ''}>Администратор</option>
                                    </select>
                                </div>
                                ${!isEdit ? `
                                <div class="mb-3">
                                    <label for="userPassword" class="form-label">Пароль *</label>
                                    <input type="password" class="form-control" id="userPassword" name="password" required>
                                </div>
                                ` : ''}
                                <div class="mb-3">
                                    <label for="userBalance" class="form-label">Баланс</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="userBalance" name="balance"
                                               value="${user ? user.balance : '0'}" min="0" step="0.01">
                                    </div>
                                </div>
                                <input type="hidden" id="userId" name="id" value="${user ? user.id : ''}">
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <button type="button" class="btn btn-primary" onclick="adminPanel.saveUser()">
                                <i class="bi bi-check"></i> ${isEdit ? 'Сохранить' : 'Создать'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем старое модальное окно если есть
        const existingModal = document.getElementById('userModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('userModal'));
        modal.show();
    }

    /**
     * Сохранить пользователя
     */
    async saveUser() {
        try {
            const form = document.getElementById('userForm');
            const formData = new FormData(form);
            const userData = Object.fromEntries(formData.entries());

            const userId = userData.id;
            const isEdit = userId && userId !== '';

            const url = isEdit ? `/api/admin/users/${userId}` : '/api/admin/users';
            const method = isEdit ? 'PUT' : 'POST';

            await this.apiRequest(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            this.showSuccess(isEdit ? 'Пользователь успешно обновлен' : 'Пользователь успешно создан');

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
            modal.hide();

            // Перезагружаем список пользователей
            this.loadUsers();

        } catch (error) {
            console.error('Ошибка сохранения пользователя:', error);
            this.showError('Ошибка сохранения пользователя: ' + error.message);
        }
    }
}

// Инициализация панели администратора
let adminPanel;
document.addEventListener('DOMContentLoaded', () => {
    adminPanel = new AdminPanel();
});

// Глобальная функция для выхода (используется в HTML)
function logout() {
    adminPanel.logout();
}
